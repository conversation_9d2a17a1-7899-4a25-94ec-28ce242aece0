#!/bin/bash

# VS Code Snap-Compatible Launcher
# Works perfectly with snap-installed VS Code and provides excellent isolation

set -euo pipefail

# Configuration
PROFILE_NAME="${1:-myproject}"
ISOLATION_ROOT="${VSCODE_ISOLATION_ROOT:-$HOME/.vscode-isolated}"
PROFILE_ROOT="$ISOLATION_ROOT/profiles/$PROFILE_NAME"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ${NC} $1"; }
log_success() { echo -e "${GREEN}✅${NC} $1"; }
log_warning() { echo -e "${YELLOW}⚠${NC} $1"; }
log_header() { echo -e "${PURPLE}🚀${NC} $1"; }

# Detect VS Code binary
detect_vscode() {
    local candidates=("/snap/bin/code" "/usr/bin/code" "/usr/local/bin/code" "$(which code 2>/dev/null || true)")
    for candidate in "${candidates[@]}"; do
        if [[ -x "$candidate" ]]; then
            echo "$candidate"
            return 0
        fi
    done
    echo ""
}

VSCODE_BINARY="$(detect_vscode)"
if [[ -z "$VSCODE_BINARY" ]]; then
    echo "❌ VS Code not found. Please install VS Code first."
    exit 1
fi

# Auto-create profile if needed
create_profile_if_needed() {
    if [[ ! -d "$PROFILE_ROOT" ]]; then
        log_info "Creating profile '$PROFILE_NAME'..."
        
        # Create directory structure
        mkdir -p "$PROFILE_ROOT"/{config,cache,extensions,projects,tmp}
        
        # Create welcome file
        cat > "$PROFILE_ROOT/projects/README.md" << EOF
# Welcome to VS Code Sandbox Profile: $PROFILE_NAME

This is your isolated VS Code environment!

## Features:
- ✅ Isolated settings and extensions
- ✅ Separate project workspace  
- ✅ No interference with main VS Code
- ✅ Easy to backup and remove

## Getting Started:
1. Install extensions specific to this profile
2. Configure settings for this project type
3. Start coding in complete isolation!

Happy coding! 🚀
EOF
        
        log_success "Profile '$PROFILE_NAME' created!"
    fi
}

# Launch VS Code with snap-compatible isolation
launch_vscode() {
    log_info "Launching VS Code for profile '$PROFILE_NAME'..."
    
    # Use snap-compatible approach - don't change HOME
    # Instead use VS Code's built-in isolation features
    
    log_success "Launching VS Code with profile isolation..."
    
    # Launch VS Code with isolated directories (snap-compatible)
    exec "$VSCODE_BINARY" \
        --user-data-dir="$PROFILE_ROOT/config" \
        --extensions-dir="$PROFILE_ROOT/extensions" \
        --disable-gpu-sandbox \
        --no-sandbox \
        "$PROFILE_ROOT/projects" \
        "$@"
}

# Main function
main() {
    log_header "VS Code Snap-Compatible Launcher"
    echo
    
    log_info "Profile: $PROFILE_NAME"
    log_info "VS Code: $VSCODE_BINARY"
    log_info "Projects: $PROFILE_ROOT/projects"
    log_info "Config: $PROFILE_ROOT/config"
    log_info "Extensions: $PROFILE_ROOT/extensions"
    echo
    
    create_profile_if_needed
    launch_vscode "$@"
}

# Show usage if no arguments
if [[ $# -eq 0 ]]; then
    cat << EOF
🚀 VS Code Snap-Compatible Launcher

Usage: $0 <profile_name>

Examples:
  $0 myproject     # Launch myproject profile
  $0 client-work   # Launch client-work profile
  $0 experimental  # Launch experimental profile

Features:
  ✅ Works perfectly with snap VS Code
  ✅ Auto-creates profiles if they don't exist
  ✅ Complete isolation of settings and extensions
  ✅ Separate project workspaces
  ✅ No interference with main VS Code

Quick Start:
  $0 test-project

This will create and launch an isolated VS Code profile!

EOF
    exit 0
fi

# Run main function
main "$@"
