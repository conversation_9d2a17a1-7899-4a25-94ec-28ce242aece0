#!/bin/bash

PROFILE_NAME=$1
PROJECT_DIR="$HOME/projects/$PROFILE_NAME"
USER_DATA_DIR="$HOME/.vscode-data/$PROFILE_NAME"
EXT_DIR="$HOME/.vscode-ext/$PROFILE_NAME"
LAUNCHER="$HOME/$PROFILE_NAME-launcher.sh"
DESKTOP_ENTRY="$HOME/.local/share/applications/code-$PROFILE_NAME.desktop"

# Wipe all existing VS Code settings and cache data (fresh start)
rm -rf "$HOME/.config/Code" \
       "$HOME/.config/Code - Insiders" \
       "$HOME/.vscode" \
       "$HOME/.cache/Code" \
       "$HOME/.cache/Code - Insiders" \
       "$HOME/.config/VSCodium" \
       "$HOME/.cache/VSCodium"

# Create directories
mkdir -p "$PROJECT_DIR" "$USER_DATA_DIR" "$EXT_DIR"

# Create launcher script with URI handling for extensions like Augment
cat > "$LAUNCHER" <<EOF
#!/bin/bash
URI="\$1"
/snap/bin/code \\
  --user-data-dir="$USER_DATA_DIR" \\
  --extensions-dir="$EXT_DIR" \\
  --open-url "\$URI"
EOF
chmod +x "$LAUNCHER"

# Create .desktop launcher with %u to pass the full vscode:// URI
cat > "$DESKTOP_ENTRY" <<EOF
[Desktop Entry]
Name=VS Code - $PROFILE_NAME
Comment=Isolated VS Code for $PROFILE_NAME
Exec=$LAUNCHER %u
Icon=code
Terminal=false
Type=Application
Categories=Development;IDE;
MimeType=x-scheme-handler/vscode;
EOF

# Register this profile to handle vscode:// URIs
xdg-mime default code-$PROFILE_NAME.desktop x-scheme-handler/vscode

# Refresh .desktop cache
update-desktop-database ~/.local/share/applications

# Install Augment extension into this isolated environment
/snap/bin/code \
  --user-data-dir="$USER_DATA_DIR" \
  --extensions-dir="$EXT_DIR" \
  --install-extension augment.vscode-augment

# Launch the isolated VS Code profile automatically
"$LAUNCHER" >/dev/null 2>&1 &

echo "✅ VS Code '$PROFILE_NAME' is ready!"
echo "👉 Run: $LAUNCHER"
echo "👉 Search: VS Code - $PROFILE_NAME"
echo "👉 vscode:// links will open in this profile"
echo "🚀 Augment extension installed and URI handling fixed"
