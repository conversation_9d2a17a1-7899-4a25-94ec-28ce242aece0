#!/bin/bash

# VS Code Sandbox Fallback Launcher
# Works without user namespace support

set -euo pipefail

PROFILE_NAME="${1:-myproject}"
PROFILE_ROOT="$HOME/.vscode-isolated/profiles/$PROFILE_NAME"
PROFILE_HOME="$PROFILE_ROOT/home"
VSCODE_BINARY="${VSCODE_BINARY:-$(which code 2>/dev/null || echo "/snap/bin/code")}"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ${NC} $1"; }
log_success() { echo -e "${GREEN}✅${NC} $1"; }
log_warning() { echo -e "${YELLOW}⚠${NC} $1"; }

# Check if profile exists
if [[ ! -d "$PROFILE_ROOT" ]]; then
    echo "❌ Profile '$PROFILE_NAME' does not exist"
    echo "💡 Create it first with: ./vscode-isolate.sh $PROFILE_NAME create"
    exit 1
fi

log_info "Launching VS Code profile '$PROFILE_NAME' in fallback mode..."

# Setup environment variables for isolation
export HOME="$PROFILE_HOME"
export XDG_CONFIG_HOME="$PROFILE_HOME/.config"
export XDG_CACHE_HOME="$PROFILE_HOME/.cache"
export XDG_DATA_HOME="$PROFILE_HOME/.local/share"
export XDG_STATE_HOME="$PROFILE_HOME/.local/state"
export XDG_RUNTIME_DIR="$PROFILE_ROOT/tmp/runtime"
export TMPDIR="$PROFILE_ROOT/tmp"
export TMP="$PROFILE_ROOT/tmp"
export TEMP="$PROFILE_ROOT/tmp"

# Create runtime directory
mkdir -p "$XDG_RUNTIME_DIR"
chmod 700 "$XDG_RUNTIME_DIR"

# Source profile environment if it exists
[[ -f "$PROFILE_HOME/.profile" ]] && source "$PROFILE_HOME/.profile"

log_success "Environment configured for profile '$PROFILE_NAME'"
log_info "Projects directory: $PROFILE_ROOT/projects"

# Launch VS Code with isolated directories
exec "$VSCODE_BINARY" \
    --user-data-dir="$XDG_CONFIG_HOME/Code" \
    --extensions-dir="$XDG_DATA_HOME/Code/extensions" \
    --disable-gpu-sandbox \
    --no-sandbox \
    "$PROFILE_ROOT/projects" \
    "$@"
