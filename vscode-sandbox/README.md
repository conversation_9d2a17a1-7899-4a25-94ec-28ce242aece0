# VS Code Sandbox

🚀 **Complete VS Code isolation solution using Linux namespaces for maximum sandboxing**

VS Code Sandbox creates completely isolated VS Code environments that simulate fresh OS installations with zero shared state between profiles or interference with your host system.

## ✨ Features

- 🔒 **Complete Isolation**: Each profile runs in its own Linux namespace sandbox
- 🏠 **Fresh OS Simulation**: Every profile behaves like a brand new operating system
- 🚫 **Zero Interference**: No impact on existing VS Code installations
- 🔄 **Multiple Profiles**: Unlimited isolated environments that coexist peacefully
- 🗂️ **Advanced Management**: Backup, restore, clone, and compare profiles
- 🧪 **Well Tested**: Comprehensive test suite ensures isolation effectiveness
- 🛡️ **Safe Operations**: Non-destructive with clean removal capabilities

## 🚀 Quick Start

```bash
# Clone the repository
git clone <your-repo-url>
cd vscode-sandbox

# Make scripts executable
chmod +x *.sh

# Create your first isolated VS Code profile
./vscode-isolate.sh myproject create

# List all profiles
./vscode-isolate.sh "" list

# Launch profile management interface
./vscode-profile-manager.sh launch
```

## 📁 Project Structure

```
vscode-sandbox/
├── vscode-isolate.sh           # Main isolation script
├── vscode-profile-manager.sh   # Advanced profile management
├── vscode-isolation-test.sh    # Comprehensive test suite
├── install.sh                  # Installation script
├── examples/                   # Usage examples
│   ├── basic-usage.sh
│   ├── development-setup.sh
│   └── client-isolation.sh
├── docs/                       # Documentation
│   ├── ARCHITECTURE.md
│   ├── TROUBLESHOOTING.md
│   └── API.md
├── README.md                   # This file
├── README-Enhanced-Isolation.md # Detailed technical documentation
├── LICENSE                     # MIT License
└── .gitignore                  # Git ignore file
```

## 🛠️ Scripts Overview

### `vscode-isolate.sh` - Main Isolation Engine
The core script that creates and manages completely isolated VS Code environments.

```bash
./vscode-isolate.sh <profile_name> [create|launch|remove|list|status]
```

### `vscode-profile-manager.sh` - Advanced Management
Provides advanced utilities for profile management, backup, and comparison.

```bash
./vscode-profile-manager.sh [launch|compare|backup|restore|clone]
```

### `vscode-isolation-test.sh` - Test Suite
Comprehensive test suite to verify isolation effectiveness.

```bash
./vscode-isolation-test.sh
```

## 🎯 Use Cases

### **Development Project Isolation**
```bash
./vscode-isolate.sh frontend-project create
./vscode-isolate.sh backend-project create
./vscode-isolate.sh mobile-app create
```

### **Client Work Separation**
```bash
./vscode-isolate.sh client-alpha create
./vscode-isolate.sh client-beta create
# Complete isolation between client projects
```

### **Technology Stack Environments**
```bash
./vscode-isolate.sh python-ml create
./vscode-isolate.sh nodejs-web create
./vscode-isolate.sh rust-systems create
```

### **Experimental Development**
```bash
./vscode-isolate.sh experimental-features create
./vscode-isolate.sh plugin-testing create
# Safe environment for testing without affecting main setup
```

## 🔧 Installation

### Automatic Installation
```bash
curl -sSL https://raw.githubusercontent.com/yourusername/vscode-sandbox/main/install.sh | bash
```

### Manual Installation
```bash
git clone https://github.com/yourusername/vscode-sandbox.git
cd vscode-sandbox
chmod +x *.sh
./vscode-isolation-test.sh  # Verify installation
```

## 📋 Requirements

- **Linux** with namespace support
- **util-linux** package (`unshare` command)
- **VS Code** installed (any method: snap, deb, AppImage, etc.)
- **Bash 4.0+** with standard utilities

### Optional Enhancements
```bash
# Enable user namespaces (if not already enabled)
echo 1 | sudo tee /proc/sys/kernel/unprivileged_userns_clone
```

## 🔒 How It Works

VS Code Sandbox uses Linux namespaces to create completely isolated environments:

1. **Mount Namespaces**: Separate filesystem views
2. **PID Namespaces**: Isolated process trees
3. **IPC Namespaces**: Separate inter-process communication
4. **UTS Namespaces**: Independent hostname/domain
5. **Custom Environment**: Isolated variables and paths

Each profile gets:
- Separate home directory (`~/.vscode-isolated/profiles/name/home`)
- Isolated temp directories
- Independent VS Code configurations
- Custom desktop integration
- Unique URI schemes

## 🧪 Testing

Run the comprehensive test suite:
```bash
./vscode-isolation-test.sh
```

Tests verify:
- ✅ Complete filesystem isolation
- ✅ Process separation
- ✅ Environment variable isolation
- ✅ Desktop integration
- ✅ Multiple profile independence
- ✅ Clean removal effectiveness

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by the need for complete development environment isolation
- Built on Linux namespace technology for maximum security
- Designed for developers who value clean, isolated workflows

## 🔗 Links

- [Detailed Technical Documentation](README-Enhanced-Isolation.md)
- [Architecture Overview](docs/ARCHITECTURE.md)
- [Troubleshooting Guide](docs/TROUBLESHOOTING.md)
- [API Reference](docs/API.md)

---

**VS Code Sandbox** - Because every project deserves its own universe. 🌌
