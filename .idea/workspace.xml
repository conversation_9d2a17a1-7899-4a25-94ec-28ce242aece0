<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="40e2b0ed-ee49-4650-b91f-00cf55bcff2d" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="/bin/php" />
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 4
}]]></component>
  <component name="ProjectId" id="2yu5UR4GM6hsobAIGzYxNf5FwGk" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "SHELLCHECK.PATH": "I do mind",
    "last_opened_file_path": "/home/<USER>/projects/vscode-sandbox",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.augmentcode.intellij.settings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-PS-251.26094.133" />
        <option value="bundled-php-predefined-a98d8de5180a-c5828cf854d9-com.jetbrains.php.sharedIndexes-PS-251.26094.133" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="40e2b0ed-ee49-4650-b91f-00cf55bcff2d" name="Changes" comment="" />
      <created>1750671632914</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750671632914</updated>
      <workItem from="1750671634140" duration="106000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>